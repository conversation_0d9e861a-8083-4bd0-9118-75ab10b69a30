@import 'libs/vars';
@import 'libs/functions';
@import 'libs/mixins';
@import 'libs/vendor';
@import 'libs/breakpoints';
@import 'libs/html-grid';
@import url("fontawesome-all.min.css");
@import url("https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,300italic,400,600");

/*
	Prologue by HTML5 UP
	html5up.net | @ajlkn
	Free for personal and commercial use under the CCA 3.0 license (html5up.net/license)
*/

// Breakpoints.

	@include breakpoints((
		wide:      ( 961px,  1880px ),
		normal:    ( 961px,  1620px ),
		narrow:    ( 961px,  1320px ),
		narrower:  ( 737px,  960px  ),
		mobile:    ( null,   736px  ),
	));

// Reset.
// Based on meyerweb.com/eric/tools/css/reset (v2.0 | 20110126 | License: public domain)

	html, body, div, span, applet, object,
	iframe, h1, h2, h3, h4, h5, h6, p, blockquote,
	pre, a, abbr, acronym, address, big, cite,
	code, del, dfn, em, img, ins, kbd, q, s, samp,
	small, strike, strong, sub, sup, tt, var, b,
	u, i, center, dl, dt, dd, ol, ul, li, fieldset,
	form, label, legend, table, caption, tbody,
	tfoot, thead, tr, th, td, article, aside,
	canvas, details, embed, figure, figcaption,
	footer, header, hgroup, menu, nav, output, ruby,
	section, summary, time, mark, audio, video {
		margin: 0;
		padding: 0;
		border: 0;
		font-size: 100%;
		font: inherit;
		vertical-align: baseline;
	}

	article, aside, details, figcaption, figure,
	footer, header, hgroup, menu, nav, section {
		display: block;
	}

	body {
		line-height: 1;
	}

	ol, ul {
		list-style:none;
	}

	blockquote,	q {
		quotes: none;

		&:before,
		&:after {
			content: '';
			content: none;
		}
	}

	table {
		border-collapse: collapse;
		border-spacing: 0;
	}

	body {
		-webkit-text-size-adjust: none;
	}

	mark {
		background-color: transparent;
		color: inherit;
	}

	input::-moz-focus-inner {
		border: 0;
		padding: 0;
	}

	input, select, textarea {
		-moz-appearance: none;
		-webkit-appearance: none;
		-ms-appearance: none;
		appearance: none;
	}

/* Basic */

	// Set box model to border-box.
	// Based on css-tricks.com/inheriting-box-sizing-probably-slightly-better-best-practice
		html {
			box-sizing: border-box;
		}

		*, *:before, *:after {
			box-sizing: inherit;
		}

	body {
		background: #fff;
		font-family: 'Source Sans Pro', sans-serif;
		font-size: 19pt;
		font-weight: 300;
		line-height: 1.75em;
		color: #888;

		// Stops initial animations until page loads.
			&.is-preload {
				*, *:before, *:after {
					@include vendor('animation', 'none !important');
					@include vendor('transition', 'none !important');
				}
			}

	}

	input, textarea, select {
		font-family: 'Source Sans Pro', sans-serif;
		font-size: 19pt;
		font-weight: 300;
		line-height: 1.75em;
		color: #888;
	}

	h1, h2, h3, h4, h5, h6 {
		font-weight: 300;
		color: #666;
		line-height: 1.5em;
	}

	h1 a, h2 a, h3 a, h4 a, h5 a, h6 a {
		color: inherit;
		text-decoration: none;

		strong {
			color: #333;
		}
	}

	h2 {
		font-size: 2em;
		letter-spacing: -1px;

		&.alt {
			color: #888;

			strong {
				color: #666;
			}
		}
	}

	h3 {
		font-size: 1.5em;
	}

	header {
		margin: 0 0 2em 0;

		> p {
			margin: 1em 0 0 0;
		}
	}

	footer {
		margin: 2em 0 0 0;
	}

	strong, b {
		font-weight: 300;
		color: #666;
	}

	em, i {
		font-style: italic;
	}

	a {
		text-decoration: none;
		color: inherit;
		border-bottom: dotted 1px rgba(128, 128, 128, 0.5);
		@include vendor('transition', ('color 0.35s ease-in-out', 'border-bottom-color 0.35s ease-in-out'));
		outline: 0;

		&:hover {
			color: #E27689;
			border-bottom-color: rgba(255, 255, 255, 0);
		}
	}

	sub {
		position: relative;
		top: 0.5em;
		font-size: 0.8em;
	}

	sup {
		position: relative;
		top: -0.5em;
		font-size: 0.8em;
	}

	hr {
		border: 0;
		border-top: solid 1px #ddd;
	}

	blockquote {
		border-left: solid 0.5em #ddd;
		padding: 1em 0 1em 2em;
		font-style: italic;
	}

	p, ul, ol, dl, table {
		margin-bottom: 2em;
	}

	br.clear {
		clear: both;
	}

/* Container */

	.container {
		margin: 0 auto;
		max-width: 100%;
		width: 1400px;

		@include breakpoint('wide') {
			width: 1200px;
		}

		@include breakpoint('normal') {
			width: 960px;
		}

		@include breakpoint('narrow') {
			width: 100%;
		}

		@include breakpoint('<=narrower') {
			width: 100%;
		}

		@include breakpoint('<=mobile') {
			width: 100%;
		}
	}

/* Row */

	.row {
		@include html-grid((40px, 40px));

		@include breakpoint('wide') {
			@include html-grid((40px, 40px), 'wide');
		}

		@include breakpoint('normal') {
			@include html-grid((40px, 40px), 'normal');
		}

		@include breakpoint('narrow') {
			@include html-grid((20px, 20px), 'narrow');
		}

		@include breakpoint('<=narrower') {
			@include html-grid((20px, 20px), 'narrower');
		}

		@include breakpoint('<=mobile') {
			@include html-grid((20px, 20px), 'mobile');
		}
	}

/* Sections/Article */

	section, article {
		margin-bottom: 3em;

		> :last-child,
		> .container,
		&:last-child {
			margin-bottom: 0;
		}

		.row > & {
			margin-bottom: 0;
		}
	}

/* Image */

	.image {
		display: inline-block;
		border: 0;

		img {
			display: block;
			width: 100%;
		}

		&.avatar48 {
			width: 48px;
			height: 48px;
			background: #f00;

			img {
				width: 48px;
				height: 48px;
			}
		}

		&.fit {
			display: block;
			width: 100%;
		}

		&.featured {
			display: block;
			width: 100%;
			margin: 0 0 2em 0;
		}

		&.left {
			float: left;
			margin: 0 2em 2em 0;
		}

		&.centered {
			display: block;
			margin: 0 0 2em 0;

			img {
				margin: 0 auto;
				width: auto;
			}
		}
	}

/* List */

	ul {
		list-style: disc;
		padding-left: 1em;

		li {
			padding-left: 0.5em;
		}
	}

	ol {
		list-style: decimal;
		padding-left: 1.25em;

		li {
			padding-left: 0.25em;
		}
	}

/* Icons */

	ul.icons {
		cursor: default;
		list-style: none;
		padding-left: 0;

		li {
			display: inline-block;
			padding-left: 0;
		}

		a {
			display: inline-block;
			width: 2em;
			height: 2em;
			line-height: 2em;
			text-align: center;
			border: 0;
		}
	}

/* Form */

	form {
		label {
			display: block;
			text-align: left;
			margin-bottom: 0.5em;
		}

		input[type="text"],
		input[type="email"],
		input[type="password"],
		select,
		textarea {
			position: relative;
			-webkit-appearance: none;
			display: block;
			border: 0;
			outline: 0;
			background: #fff;
			background: rgba(255, 255, 255, 0.75);
			width: 100%;
			border-radius: 0.35em;
			padding: 0.75em 1em 0.75em 1em;
			box-shadow: inset 0 0.1em 0.1em 0 rgba(0, 0, 0, 0.05);
			border: solid 1px rgba(0, 0, 0, 0.15);
			@include vendor('transition', 'all 0.35s ease-in-out');

			&:focus {
				box-shadow: 0 0 2px 1px #8ebebc;
				background: #fff;
			}
		}

		input[type="text"],
		input[type="email"],
		input[type="password"],
		select {
			line-height: 1.25em;
		}

		textarea {
			min-height: 14em;
		}

		::-webkit-input-placeholder {
			color: #555 !important;
		}

		:-moz-placeholder {
			color: #555 !important;
		}

		::-moz-placeholder {
			color: #555 !important;
		}

		:-ms-input-placeholder {
			color: #555 !important;
		}

		::-moz-focus-inner {
			border: 0;
		}
	}

/* Table */

	table {
		width: 100%;
		text-align: left;

		tbody {
			tr {
				&:nth-child(2n+2) {
					background: #f4f4f4;
				}
			}
		}

		td {
			padding: 0.5em 1em 0.5em 1em;
		}

		th {
			text-align: left;
			padding: 0.5em 1em 0.5em 1em;
			color: #fff;
			background: #222729 url('images/overlay.png');
		}

		thead {
			background: #444;
			color: #fff;
		}

		tfoot {
			background: #eee;
		}
	}

/* Button */

	input[type="button"],
	input[type="submit"],
	input[type="reset"],
	button,
	.button {
		position: relative;
		display: inline-block;
		border-radius: 0.35em;
		color: #fff !important;
		text-decoration: none;
		padding: 0.75em 2.5em 0.75em 2.5em;
		background-color: #8ebebc;
		border: 0;
		cursor: pointer;
		@include vendor('background-image', ('linear-gradient(top, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.15))', 'url("images/overlay.png")'));
		@include vendor('transition', 'background-color 0.35s ease-in-out');

		&:hover {
			background-color: #9ececc;
		}

		&:active {
			background-color: #7eaeac;
		}
	}

/* Item */

	.item {
		box-shadow: 0 0.05em 0.15em 0 rgba(0, 0, 0, 0.05);
		margin-bottom: 40px;

		header {
			background: #fff;
			margin: 0;
			padding: 1em 0 1em 0;
			font-size: 0.8em;

			h3 {
				font-size: 1em;
			}
		}
	}

/* Icons */

	.icon {
		@include icon;
		text-decoration: none;

		&:before {
			line-height: inherit;
		}

		> .label {
			display: none;
		}

		&.solid {
			&:before {
				font-weight: 900;
			}
		}

		&.brands {
			&:before {
				font-family: 'Font Awesome 5 Brands';
			}
		}
	}

/* Header */

	#header {
		@include vendor('display', 'flex');
		@include vendor('flex-direction', 'column');
		@include vendor('justify-content', 'space-between');
		background: #222629 url('images/overlay.png');
		box-shadow: inset -0.25em 0 0.25em 0 rgba(0, 0, 0, 0.1);
		color: #fff;
		height: 100%;
		left: 0;
		overflow-y: auto;
		position: fixed;
		text-align: right;
		top: 0;
		width: 375px;

		.top {
			@include vendor('flex-grow', '1');
		}

		.bottom {
			@include vendor('flex-shrink', '0');
			padding: 1.5em 0 0.75em 0;

			> :last-child {
				margin-bottom: 0;
			}
		}

		.icons {
			font-size: 0.8em;
			text-align: center;

			a {
				color: #41484c;
				@include vendor('transition', 'color 0.35s ease-in-out');

				&:hover {
					color: #fff;
				}
			}
		}
	}

	#logo {
		position: relative;
		margin: 1.75em 1.5em 1.5em 1.5em;
		min-height: 48px;
		cursor: default;

		h1 {
			position: relative;
			color: #fff;
			font-weight: 600;
			font-size: 1em;
			line-height: 1em;
		}

		p {
			position: relative;
			display: block;
			font-size: 0.6em;
			color: rgba(255, 255, 255, 0.5);
			line-height: 1.25em;
			margin: 0.5em 0 0 0;
		}

		.image {
			position: absolute;
			left: 0;
			top: 0;
		}
	}

	#nav {
		ul {
			list-style: none;
			padding-left: 0;
			margin-bottom: 0;

			li {
				padding-left: 0;

				a {
					display: block;
					padding: 0.5em 1.5em 0.5em 1.5em;
					color: rgba(255, 255, 255, 0.5);
					text-decoration: none;
					outline: 0;
					border: 0;
					@include vendor('transition', 'none');

					span {
						position: relative;
						display: block;
						font-size: 0.8em;

						&:before {
							position: absolute;
							left: 0;
							color: #41484c;
							text-align: center;
							width: 1.25em;
							line-height: 1.75em;
						}
					}

					&.active {
						background: rgba(0, 0, 0, 0.15);
						box-shadow: inset 0 0 0.25em 0 rgba(0, 0, 0, 0.125);
						color: #fff;

						span:before {
							color: #e27689;
						}
					}
				}
			}
		}
	}

/* Footer */

	#footer {
		margin-left: 375px;
		text-align: center;
		background-color: #dce3e2;
		padding: 3em 0 4em 0;
		box-shadow: inset 0 1px 0 0 rgba(0, 0, 0, 0.05), inset 0 0.1em 0.1em 0 rgba(0, 0, 0, 0.025);
		font-size: 0.8em;

		.copyright {
			cursor: default;
			margin: 0;

			li {
				display: inline-block;
				line-height: 1em;
				border-left: solid 1px rgba(128, 128, 128, 0.35);
				padding: 0 0 0 0.5em;
				margin: 0 0 0 0.5em;

				&:first-child {
					border-left: 0;
					padding-left: 0;
					margin-left: 0;
				}
			}
		}
	}

/* Main */

	#main {
		margin-left: 375px;

		> section {
			margin: 0;
			overflow: hidden;
			padding: 4em 0;
			box-shadow: inset 0 1px 0 0 rgba(0, 0, 0, 0.05), inset 0 0.1em 0.1em 0 rgba(0, 0, 0, 0.025);
			text-align: center;
			background-image: url('images/overlay.png');

			&.dark {
				color: #ddd;
				color: rgba(255, 255, 255, 0.75);

				h2, h3, h4, h5, h6 {
					color: inherit;
				}

				strong {
					color: #fff;
					border-color: inherit;
				}

				a {
					color: #fff;
					border-color: inherit;

					&:hover {
						border-bottom-color: rgba(255, 255, 255, 0);
					}
				}
			}

			&.cover {
				padding: 10em 0;
				background-size: cover;
				background-position: center center;
			}

			&.one {
				background-color: #81918E;
				background-image: url('../../images/banner.jpg');
			}

			&.two {
				background-color: #f5fafa;
			}

			&.three {
				background-color: #ecf1f1;
			}

			&.four {
				background-color: #e8edec;
			}
		}
	}

/* Wide */

	@include breakpoint('wide') {

		/* Basic */

			body, input, textarea, select {
				font-size: 17pt;
			}

		/* Header */

			#header {
				width: 300px;
			}

		/* Footer */

			#footer {
				margin-left: 300px;
			}

		/* Main */

			#main {
				margin-left: 300px;
			}

	}

/* Normal */

	@include breakpoint('normal') {

		/* Main */

			#main {
				> section {
					padding: 3em 0;
				}

				section {
					&.cover {
						padding: 5em 0;
					}
				}
			}

	}

/* Narrow */

	@include breakpoint('narrow') {

		/* Basic */

			body, input, textarea, select {
				font-size: 16pt;
			}

			.container {
				padding: 0 2em 0 2em;
			}

		/* Icons */

			ul.icons {
				li {
					a {
						width: 1.75em;
					}
				}
			}

		/* Item */

			.item {
				margin-bottom: 20px;
			}

		/* Header */

			#header {
				width: 20%;
			}

			#logo {
				.image {
					position: relative;
					margin: 0 0 0.5em 0;
				}
			}

			#nav {
				ul {
					li {
						a {
							font-size: 0.8em;
							padding-top: 0.5em;
							padding-bottom: 0.5em;

							span {
								padding-right: 2.25em;

								&:before {
									left: 100%;
									margin-left: -1.25em;
									line-height: 2.25em;
								}
							}
						}
					}
				}
			}

		/* Footer */

			#footer {
				margin-left: 20%;
			}

		/* Main */

			#main {
				margin-left: 20%;
			}

	}

/* Narrower */

	#headerToggle {
		display: none;
	}

	@include breakpoint('<=narrower') {

		/* Basic */

			html, body {
				overflow-x: hidden;
			}

			body, input, textarea, select {
				font-size: 16pt;
			}

			header {
				br {
					display: none;
				}
			}

			.container {
				padding: 0 2em 0 2em;
			}

		/* Item */

			.item {
				margin-bottom: 15px;
			}

		/* Icons */

			ul.icons {
				a {
					width: 1.75em;
					font-size: 1.25em;
				}
			}

		/* Header */

			#header {
				@include vendor('backface-visibility', 'hidden');
				@include vendor('transform', 'translateX(#{_size(header) * -1})');
				@include vendor('transition', ('transform #{_duration(header)} ease'));
				-webkit-overflow-scrolling: touch;
				display: block;
				height: 100%;
				left: 0;
				overflow-y: auto;
				position: fixed;
				top: 0;
				width: _size(header);
				z-index: _misc(z-index-base) + 2;
				width: _size(header);
				background: #222729 url('images/overlay.png');
				box-shadow: inset -0.25em 0 0.25em 0 rgba(0, 0, 0, 0.125);

				.top {
					position: relative;
				}

				.bottom {
					border-top: solid 1px rgba(255, 255, 255, 0.05);
					box-shadow: 0 -1px 0 0 rgba(0, 0, 0, 0.15);
					padding-top: 2em;
					margin-top: 2em;
					position: relative;
				}
			}

			#logo {
				margin: 1.5em 1.25em 1.25em 1.25em;
			}

			#nav {
				ul {
					li {
						a {
							padding: 0.5em 1.25em 0.5em 1.25em;
						}
					}
				}
			}

			#headerToggle {
				@include vendor('backface-visibility', 'hidden');
				@include vendor('transition', 'transform #{_duration(header)} ease');
				display: block;
				height: 2.25em;
				left: 0;
				position: fixed;
				top: 0;
				width: 3.25em;
				z-index: _misc(z-index-base) + 1;

				.toggle {
					@include icon(false, solid);
					position: absolute;
					left: 0;
					top: 0;
					width: 100%;
					height: 100%;
					outline: 0;
					border: 0;

					&:before {
						text-decoration: none;
						content: '\f0c9';
						color: #fff;
						font-size: 18px;
						background: rgba(128, 136, 144, 0.5);
						border-radius: 0.35em;
						text-align: center;
						position: absolute;
						left: 0.5em;
						top: 0.5em;
						display: block;
						width: 3.25em;
						height: 2.25em;
						line-height: 2.25em;
					}
				}
			}

			body {
				&.header-visible {
					#main {
						@include vendor('transform', 'translateX(#{_size(header)})');
					}

					#headerToggle {
						@include vendor('transform', 'translateX(#{_size(header)})');
					}

					#header {
						@include vendor('transform', 'translateX(0)');
					}
				}
			}

		/* Footer */

			#footer {
				margin-left: 0;
			}

		/* Main */

			#main {
				@include vendor('backface-visibility', 'hidden');
				@include vendor('transition', 'transform #{_duration(header)} ease');
				padding-bottom: 1px;
				margin-left: 0;

				> section {
					padding: 3em 0;
				}

				section {
					&.cover {
						padding: 4em 0;
					}
				}
			}
	}

/* Mobile */

	@include breakpoint('<=mobile') {

		/* Basic */

			body, input, textarea, select {
				font-size: 14pt;
			}

			h2 {
				font-size: 1.5em;
				letter-spacing: 0;
				font-weight: 300;
			}

			.container {
				padding: 0 15px 0 15px;
			}

		/* List */

			ul.icons {
				a {
					width: 2em;
					font-size: 1.25em;
				}
			}

		/* Main */

			#main {
				> section {
					padding: 2em 0;
				}

				section {
					&.cover {
						padding: 4em 0em;

						header {
							padding: 0 1em;
						}
					}
				}
			}

		/* Footer */

			#footer {
				.copyright {
					li {
						display: block;
						line-height: 1.25em;
						border: 0;
						padding: 0;
						margin: 1em 0 0 0;

						&:first-child {
							margin-top: 0;
						}
					}
				}
			}

	}